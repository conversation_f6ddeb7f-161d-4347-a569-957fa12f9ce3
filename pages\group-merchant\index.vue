<template>
  <snowy-layout title="集团商户" :isTabbar="false" :isFirstPage="true">
    <view class="container">
      <!-- 搜索框 -->
      <view class="search-container">
        <view class="search-box">
          <uni-icons type="search" size="18" color="#999" class="search-icon"></uni-icons>
          <input class="search-input" placeholder="可输入姓名、证件号码、手机号进行查询" v-model="searchKey" @input="onSearchInput" />
        </view>
      </view>

      <!-- 商户列表 -->
      <scroll-view class="merchant-list" scroll-y="true" @scrolltolower="loadMore" refresher-enabled
        :refresher-triggered="refreshing" @refresherrefresh="onRefresh">
        <view class="merchant-item" v-for="(item, index) in merchantList" :key="index"
          @click="viewMerchantDetail(item)">
          <view class="merchant-header">
            <view class="merchant-name">{{ item.name || '' }}</view>
            <view class="merchant-action">
              <view class="action-btn" @click.stop="viewMerchantBoard(item)">商户看板</view>
            </view>
          </view>

          <view class="merchant-info">
            <view class="info-row">
              <view class="info-label">法人名称</view>
              <view class="info-value">{{ item.idName || '' }}</view>
            </view>

            <view class="info-row">
              <view class="info-label">法人手机号</view>
              <view class="info-value">{{ item.idPhone || '' }}</view>
            </view>

            <view class="info-row">
              <view class="info-label">办公地址</view>
              <view class="info-value">{{ item.workAddress || '' }}</view>
            </view>
          </view>
        </view>

        <!-- 加载状态 -->
        <view class="loading-more" v-if="loading">加载中...</view>
        <!-- <view class="no-more" v-if="noMore">没有更多数据了</view> -->

        <!-- 空状态 -->
        <view class="empty-state" v-if="!loading && merchantList.length === 0">
          <view class="empty-text">暂无商户数据</view>
        </view>
      </scroll-view>
    </view>

  </snowy-layout>
</template>

<script>
import bizGroupMerchantApi from '@/api/biz/bizGroupMerchantApi'
import store from '@/store'

export default {
  data () {
    return {
      searchKey: '',
      merchantList: [],
      loading: false,
      noMore: false,
      refreshing: false,
      current: 1,
      size: 10,
      searchTimer: null
    }
  },

  onLoad () {
    // 隐藏tabbar
    uni.hideTabBar()
    this.loadMerchantList()
  },

  // 使用scroll-view后不再需要onReachBottom和onPullDownRefresh

  methods: {
    /**
     * 加载商户列表
     */
    async loadMerchantList (isRefresh = false) {
      if (this.loading) return Promise.resolve()

      this.loading = true

      try {
        const params = {
          parentMerchantId: store.getters.entityId, // 取当前用户的entityId作为集团商户ID
          current: isRefresh ? 1 : this.current,
          size: this.size
        }

        if (this.searchKey.trim()) {
          params.searchKey = this.searchKey.trim()
        }
        const res = await bizGroupMerchantApi.getGroupMerchantList(params)

        if (res) {
          const newList = res.records || []

          if (isRefresh) {
            this.merchantList = newList
            this.current = 1
          } else {
            this.merchantList = [...this.merchantList, ...newList]
          }

          // 判断是否还有更多数据
          this.noMore = newList.length < this.size

          if (!isRefresh) {
            this.current++
          }
        } else {
          uni.showToast({
            title:'获取商户列表失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('获取商户列表失败:', error)
        uni.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        })
      } finally {
        this.loading = false
        if (isRefresh) {
          uni.stopPullDownRefresh()
        }
      }
    },

    /**
     * 搜索输入处理
     */
    onSearchInput () {
      // 防抖处理
      if (this.searchTimer) {
        clearTimeout(this.searchTimer)
      }

      this.searchTimer = setTimeout(() => {
        this.refreshList()
      }, 500)
    },

    /**
     * 下拉刷新
     */
    onRefresh () {
      this.refreshing = true
      this.current = 1
      this.noMore = false
      this.loadMerchantList(true).finally(() => {
        this.refreshing = false
      })
    },

    /**
     * 刷新列表
     */
    refreshList () {
      this.current = 1
      this.noMore = false
      this.loadMerchantList(true)
    },

    /**
     * 加载更多
     */
    loadMore () {
      if (this.loading || this.noMore) return
      this.loadMerchantList()
    },

    /**
     * 查看商户详情
     */
    viewMerchantDetail (merchant) {
      // 这里可以跳转到商户详情页面
      uni.showToast({
        title: `查看${merchant.name || '商户'}详情`,
        icon: 'none'
      })
    },

    /**
     * 查看商户看板
     */
    viewMerchantBoard(merchant) {
      // 跳转到商户看板页面，传递商户ID参数
      uni.navigateTo({
        url: `/pages/merchant-board/index?merchantId=${merchant.merchantId || '99'}`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.search-container {
  padding: 32rpx;
  background: #fff;
  border-bottom: 1rpx solid #f5f5f5;
}

.search-box {
  display: flex;
  align-items: center;
  background: #f8f8f8;
  border-radius: 48rpx;
  padding: 20rpx 32rpx;

  .search-icon {
    color: #999;
    margin-right: 16rpx;
    font-size: 32rpx;
  }

  .search-input {
    flex: 1;
    font-size: 28rpx;
    color: #333;

    &::placeholder {
      color: #999;
    }
  }
}

.merchant-list {
  padding:0 26rpx;
  flex: 1;
  /* 减去搜索框和状态栏的高度 */
  box-sizing: border-box;
  overflow: auto;
}

.merchant-item {
  background: #fff;
  border-radius: 16rpx;
  margin: 24rpx 0;
  padding: 32rpx;
  // box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.merchant-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;

  .merchant-name {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }

  .merchant-action {
    .action-btn {
      background: #FFD700;
      color: #333;
      padding: 18rpx 28rpx;
      border-radius: 40rpx;
      font-size: 26rpx;
      font-weight: 500;
    }
  }
}

.merchant-info {
  .info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 22rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .info-label {
      font-size: 28rpx;
      color: #666;
    }

    .info-value {
      font-size: 28rpx;
      color: rgba(0, 0, 0, 0.60);
      text-align: right;
      flex: 1;
      margin-left: 32rpx;
    }
  }
}

// 加载状态
.loading-more,
.no-more {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  padding: 20rpx 0;
}

.empty-state {
  padding: 120rpx 40rpx;
  text-align: center;

  .empty-text {
    color: #999;
    font-size: 28rpx;
  }
}
</style>
